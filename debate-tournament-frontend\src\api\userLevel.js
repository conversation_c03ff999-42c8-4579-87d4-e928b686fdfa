import request from './index'

/**
 * 用户等级系统相关API
 */

// 获取用户等级信息
export function getUserLevel(userId) {
  return request.get(`/user-level/${userId}`)
}

// 获取当前用户等级信息
export function getCurrentUserLevel() {
  return request.get('/user-level/current')
}

// 添加经验值
export function addExperience(experienceData) {
  return request.post('/user-level/experience', experienceData)
}

// 更新辩论统计
export function updateDebateStats(statsData) {
  return request.put('/user-level/debate-stats', statsData)
}

// 获取用户排行榜
export function getLeaderboard(type = 'level', page = 0, size = 20) {
  return request.get('/user-level/leaderboard', {
    type,
    page,
    size
  })
}

// 获取成就列表
export function getAchievements(userId) {
  return request.get(`/user-level/${userId}/achievements`)
}

// 解锁成就
export function unlockAchievement(achievementData) {
  return request.post('/user-level/achievement', achievementData)
}

// 获取称号列表
export function getTitles(userId) {
  return request.get(`/user-level/${userId}/titles`)
}

// 设置活跃称号
export function setActiveTitle(titleId) {
  return request.post(`/user-level/title/${titleId}/activate`)
}

// 获取等级经验配置
export function getLevelConfig() {
  return request.get('/user-level/config')
}
