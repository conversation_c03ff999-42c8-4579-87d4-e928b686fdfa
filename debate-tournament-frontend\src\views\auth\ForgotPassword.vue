<template>
  <div class="auth-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="auth-container single-panel">
      <!-- 表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">找回密码</h2>
            <p class="form-subtitle">我们将向您的邮箱发送重置链接</p>
          </div>

          <!-- 错误和成功消息 -->
          <div v-if="errors.general" class="error-message">
            {{ errors.general }}
          </div>

          <div v-if="success" class="success-message">
            {{ success }}
          </div>

          <el-form v-if="!success" ref="forgotForm" :model="formData" :rules="rules" class="auth-form"
            @submit.prevent="handleSubmit" size="large">
            <!-- 邮箱输入 -->
            <el-form-item prop="email">
              <el-input v-model="formData.email" placeholder="请输入您注册时使用的邮箱" prefix-icon="Message" clearable />
            </el-form-item>

            <!-- 验证码输入 -->
            <el-form-item prop="captcha">
              <div class="captcha-wrapper">
                <el-input v-model="formData.captcha" placeholder="请输入验证码" prefix-icon="Key" maxlength="4" clearable
                  class="captcha-input" />
                <div class="captcha-image-container" @click="refreshCaptcha">
                  <el-image v-if="captchaUrl" :src="captchaUrl" fit="fill" class="captcha-img"
                    :loading="captchaLoading">
                    <template #error>
                      <div class="captcha-error">
                        <el-icon>
                          <Warning />
                        </el-icon>
                        <span>点击刷新</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else class="captcha-loading">
                    <el-icon class="is-loading">
                      <Loading />
                    </el-icon>
                  </div>
                  <div class="captcha-refresh-hint">点击刷新</div>
                </div>
              </div>
            </el-form-item>

            <!-- 提交按钮 -->
            <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading" size="large" round>
              {{ loading ? '发送中...' : '发送重置链接' }}
            </el-button>

            <!-- 返回登录链接 -->
            <div class="login-prompt">
              <router-link to="/login" class="login-link">返回登录</router-link>
            </div>
          </el-form>

          <!-- 成功状态 -->
          <div v-if="success" class="status-container">
            <div class="status-icon success">
              <el-icon>
                <CircleCheck />
              </el-icon>
            </div>
            <h3 class="status-title">邮件已发送</h3>
            <p class="status-message">
              重置密码的链接已发送到您的邮箱，请查收并按照邮件中的指引重置密码。
            </p>
            <div class="status-actions">
              <el-button type="primary" @click="goToLogin">返回登录</el-button>
              <el-button @click="resendEmail" :loading="loading">重新发送</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ForgotPasswordLogic from './js/ForgotPassword.js';

// 使用忘记密码逻辑
const forgotPasswordLogic = ForgotPasswordLogic.setup();

// 解构需要的变量和方法
const {
  formData,
  errors,
  loading,
  success,
  captchaUrl,
  captchaLoading,
  handleSubmit,
  refreshCaptcha,
  goToLogin,
  resendEmail,
  rules,
  forgotForm
} = forgotPasswordLogic;
</script>

<style lang="scss" scoped>
@use './scss/Auth.scss';
</style>
