import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import { register, generateCaptcha, getCaptchaImage, refreshCaptcha as refreshCaptchaAPI } from '@/api/auth';
import { useEncryptionStore } from '@/store/encryption';
import { ElMessage } from 'element-plus';

// 验证表单
export const validateForm = async (formRef) => {
  if (!formRef) return false;
  try {
    await formRef.validate();
    return true;
  } catch (error) {
    return false;
  }
}

function useRegister() {
    const router = useRouter()
    const userStore = useUserStore()
    const registerForm = ref(null)

    const formData = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      captcha: '',
      agreeTerms: false
    })

    // 校验密码匹配
    const validatePasswordMatch = (rule, value, callback) => {
      if (value !== formData.password) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };

    // Element Plus表单验证规则
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在3到20个字符之间', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, message: '密码长度不能少于8个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        { validator: validatePasswordMatch, trigger: 'blur' }
      ],
      captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 4, message: '验证码必须是4位', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            // 这里的 currentCaptcha 只在 setup 里有用，导出方法不依赖它
            callback();
          },
          trigger: 'blur'
        }
      ],
      agreeTerms: [
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请同意服务条款和隐私政策'));
            } else {
              callback();
            }
          },
          trigger: 'change'
        }
      ]
    }

    const errors = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      captcha: '',
      agreeTerms: '',
      general: ''
    })

    const loading = ref(false)
    const showPassword = reactive({
      password: false,
      confirmPassword: false
    })

    // 验证码相关状态
    const captchaUrl = ref('')
    const captchaSessionId = ref('')
    const captchaLoading = ref(false)

    // 初始化验证码
    const initCaptcha = async () => {
      try {
        captchaLoading.value = true
        const response = await generateCaptcha()
        captchaSessionId.value = response.data.sessionId
        await loadCaptchaImage()
      } catch (error) {
        console.error('初始化验证码失败:', error)
        ElMessage.error('验证码加载失败，请刷新页面重试')
      } finally {
        captchaLoading.value = false
      }
    }

    // 加载验证码图片
    const loadCaptchaImage = async () => {
      if (!captchaSessionId.value) return
      try {
        const response = await getCaptchaImage(captchaSessionId.value)
        captchaUrl.value = `data:image/png;base64,${response.data.image}`
      } catch (error) {
        console.error('加载验证码图片失败:', error)
        ElMessage.error('验证码图片加载失败')
      }
    }

    // 刷新验证码
    const refreshCaptcha = async () => {
      if (!captchaSessionId.value) {
        await initCaptcha()
        return
      }

      try {
        captchaLoading.value = true
        await refreshCaptchaAPI(captchaSessionId.value)
        await loadCaptchaImage()
        formData.captcha = '' // 清空输入框
      } catch (error) {
        console.error('刷新验证码失败:', error)
        ElMessage.error('刷新验证码失败，请重试')
      } finally {
        captchaLoading.value = false
      }
    }

    const handleRegister = async () => {
      if (!registerForm.value) return;

      await registerForm.value.validate(async (valid) => {
        if (!valid) return;

        loading.value = true;
        errors.general = '';

        try {
          const registerData = {
            username: formData.username,
            email: formData.email,
            password: formData.password,
            confirmPassword: formData.confirmPassword,
            captcha: formData.captcha,
            sessionId: captchaSessionId.value
          };

          const response = await register(registerData);

          if (response.success) {
            ElMessage.success('注册成功！');
            router.push('/login');
          }
        } catch (error) {
          console.error('注册失败:', error);

          if (error.response?.status === 400) {
            const errorData = error.response.data;
            if (errorData.field === 'captcha') {
              errors.captcha = errorData.message;
              await refreshCaptcha(); // 刷新验证码
            } else if (errorData.field) {
              errors[errorData.field] = errorData.message;
            } else {
              errors.general = errorData.message || '注册失败，请重试';
            }
          } else {
            errors.general = error.response?.data?.message || '注册失败，请重试';
          }
        } finally {
          loading.value = false;
        }
      });
    }

    const togglePasswordVisibility = (field) => {
      showPassword[field] = !showPassword[field]
    }

    const goToLogin = () => {
      router.push('/login')
    }

    // 组件挂载时初始化验证码
    onMounted(() => {
      initCaptcha();
    })

    return {
      formData,
      errors,
      loading,
      showPassword,
      captchaUrl,
      captchaLoading,
      handleRegister,
      togglePasswordVisibility,
      refreshCaptcha,
      goToLogin,
      rules,
      registerForm
    }
}

// 导出默认对象
export default {
  setup: useRegister
}
