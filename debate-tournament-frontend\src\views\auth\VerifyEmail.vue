<template>
  <div class="auth-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="auth-container single-panel">
      <!-- 表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">邮箱验证</h2>
            <p class="form-subtitle">验证您的邮箱地址</p>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="status-container">
            <div class="status-icon">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
            </div>
            <h3 class="status-title">正在验证</h3>
            <p class="status-message">正在验证您的邮箱，请稍候...</p>
          </div>

          <!-- 验证失败 -->
          <div v-else-if="error" class="status-container">
            <div class="status-icon error">
              <el-icon>
                <CircleClose />
              </el-icon>
            </div>
            <h3 class="status-title">验证失败</h3>
            <p class="status-message">{{ error }}</p>
            <div class="status-actions">
              <el-button type="primary" @click="resendVerification" :loading="resendLoading">
                {{ resendLoading ? '发送中...' : '重新发送验证邮件' }}
              </el-button>
              <el-button @click="goToLogin">返回登录</el-button>
            </div>
          </div>

          <!-- 验证成功 -->
          <div v-else-if="success" class="status-container">
            <div class="status-icon success">
              <el-icon>
                <CircleCheck />
              </el-icon>
            </div>
            <h3 class="status-title">验证成功</h3>
            <p class="status-message">
              您的邮箱已成功验证，现在可以使用所有功能了。
            </p>
            <div class="status-actions">
              <el-button type="primary" @click="goToHome">前往首页</el-button>
              <el-button @click="goToLogin">返回登录</el-button>
            </div>
          </div>

          <!-- 等待验证 -->
          <div v-else class="status-container">
            <div class="status-icon warning">
              <el-icon>
                <Warning />
              </el-icon>
            </div>
            <h3 class="status-title">等待验证</h3>
            <p class="status-message">
              请检查您的邮箱并点击验证链接完成邮箱验证。
            </p>
            <div class="status-actions">
              <el-button type="primary" @click="resendVerification" :loading="resendLoading">
                {{ resendLoading ? '发送中...' : '重新发送验证邮件' }}
              </el-button>
              <el-button @click="goToLogin">返回登录</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import VerifyEmailLogic from './js/VerifyEmail.js';

// 使用邮箱验证逻辑
const verifyEmailLogic = VerifyEmailLogic.setup();

// 解构需要的变量和方法
const {
  loading,
  error,
  success,
  resendLoading,
  resendVerification,
  goToHome,
  goToLogin
} = verifyEmailLogic;
</script>

<style lang="scss" scoped>
@use './scss/Auth.scss';
</style>
