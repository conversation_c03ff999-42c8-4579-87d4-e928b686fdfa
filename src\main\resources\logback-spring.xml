<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件的存储地址 -->
    <property name="LOG_HOME" value="logs" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 异步日志 -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 特定包的日志级别 -->
    <logger name="com.debate_ournament" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
    </logger>

    <!-- 减少Spring框架日志 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.boot" level="INFO"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.data" level="WARN"/>

    <!-- 完全禁用Hibernate日志 -->
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="org.hibernate.SQL" level="ERROR"/>
    <logger name="org.hibernate.type" level="ERROR"/>
    <logger name="org.hibernate.orm" level="ERROR"/>
    <logger name="org.hibernate.engine" level="ERROR"/>
    <logger name="org.hibernate.stat" level="ERROR"/>

    <!-- 减少连接池日志 -->
    <logger name="com.zaxxer.hikari" level="WARN"/>

    <!-- 减少Tomcat日志 -->
    <logger name="org.apache.catalina" level="WARN"/>
    <logger name="org.apache.tomcat" level="WARN"/>
    <logger name="org.apache.coyote" level="WARN"/>

    <!-- 减少其他框架日志 -->
    <logger name="io.lettuce" level="WARN"/>
    <logger name="reactor.netty" level="WARN"/>

    <!-- 根日志级别 -->
    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
    </root>

    <!-- 开发环境配置 -->
    <springProfile name="!prod">
        <logger name="com.debate_ournament.users.service" level="INFO"/>
        <logger name="com.debate_ournament.security" level="WARN"/>
    </springProfile>

    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <logger name="com.debate_ournament" level="INFO"/>
        <root level="WARN"/>
    </springProfile>
</configuration>
