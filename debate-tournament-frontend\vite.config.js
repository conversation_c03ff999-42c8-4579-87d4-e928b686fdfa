import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 移除 additionalData 以避免与手动导入冲突
      }
    }
  },
  define: {
    // 确保在生产环境中不显示Lit开发模式警告
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    // 禁用Lit开发模式
    'global.litIsInSSR': false,
    'globalThis.litIsInSSR': false
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: '../src/main/resources/static',
    emptyOutDir: true,
    chunkSizeWarningLimit: 1500
  }
});
