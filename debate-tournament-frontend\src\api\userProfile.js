import request from './index'
/**
 * 用户资料相关API
 */
// 获取用户资料
export function getUserProfile(userId) {
  return request.get(`/users/profile/${userId}`)
}

// 获取当前用户资料
export function getCurrentUserProfile() {
  return request.get('/users/profile/current')
}

// 更新用户资料
export function updateUserProfile(profileData) {
  return request.put('/users/profile/my', profileData)
}

// 上传头像
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/users/profile/my/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除头像
export function deleteAvatar() {
  return request.delete('/users/profile/my/avatar')
}

// 获取用户资料列表（管理员功能）
export function getUserProfiles(page = 0, size = 10, search = '') {
  return request.get('/users/profile/list', {
    page,
    size,
    search
  })
}

// 更新资料可见性设置
export function updateProfileVisibility(visibilitySettings) {
  return request.put('/users/profile/my/visibility', visibilitySettings)
}
