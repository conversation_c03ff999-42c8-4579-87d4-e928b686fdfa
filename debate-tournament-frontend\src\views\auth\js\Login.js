import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import { login, generateCaptcha, getCaptchaImage, refreshCaptcha as refreshCaptchaAPI } from '@/api/auth';
import { useEncryptionStore } from '@/store/encryption';
import { ElMessage } from 'element-plus';

// 验证表单
export const validateForm = async (formRef) => {
  if (!formRef) return false;

  try {
    await formRef.validate();
    return true;
  } catch (error) {
    return false;
  }
}

function useLogin() {
    const router = useRouter()
    const userStore = useUserStore()
    const loginForm = ref(null)

    const formData = reactive({
      username: '',
      password: '',
      captcha: '',
      rememberMe: false
    })

    // Element Plus表单验证规则
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在3到20个字符之间', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
      ],
      captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 4, message: '验证码必须是4位', trigger: 'blur' }
      ]
    }

    const errors = reactive({
      username: '',
      password: '',
      captcha: '',
      general: ''
    })

    const loading = ref(false)
    const showPassword = ref(false)

    // 验证码相关状态
    const captchaUrl = ref('')
    const captchaSessionId = ref('')
    const captchaLoading = ref(false)

    // 初始化验证码
    const initCaptcha = async () => {
      try {
        captchaLoading.value = true
        const response = await generateCaptcha()
        captchaSessionId.value = response.data.sessionId
        await loadCaptchaImage()
      } catch (error) {
        console.error('初始化验证码失败:', error)
        ElMessage.error('验证码加载失败，请刷新页面重试')
      } finally {
        captchaLoading.value = false
      }
    }

    // 加载验证码图片
    const loadCaptchaImage = async () => {
      if (!captchaSessionId.value) return
      try {
        const response = await getCaptchaImage(captchaSessionId.value)
        captchaUrl.value = response.data.image // 后端已经返回完整的Data URL
      } catch (error) {
        console.error('加载验证码图片失败:', error)
        ElMessage.error('验证码图片加载失败')
      }
    }

    // 刷新验证码
    const refreshCaptcha = async () => {
      if (!captchaSessionId.value) {
        await initCaptcha()
        return
      }

      try {
        captchaLoading.value = true
        await refreshCaptchaAPI(captchaSessionId.value)
        await loadCaptchaImage()
        formData.captcha = '' // 清空输入框
      } catch (error) {
        console.error('刷新验证码失败:', error)
        ElMessage.error('刷新验证码失败，请重试')
      } finally {
        captchaLoading.value = false
      }
    }

    const handleLogin = async () => {
      if (!loginForm.value) return;

      await loginForm.value.validate(async (valid) => {
        if (!valid) return;

        loading.value = true;
        errors.general = '';

        try {
          const loginData = {
            username: formData.username,
            password: formData.password,
            captcha: formData.captcha,
            sessionId: captchaSessionId.value
          };

          const response = await login(loginData);

          if (response.success) {
            userStore.setUser(response.data.user);
            userStore.setToken(response.data.token);

            if (formData.rememberMe) {
              localStorage.setItem('rememberMe', 'true');
              localStorage.setItem('username', formData.username);
            } else {
              localStorage.removeItem('rememberMe');
              localStorage.removeItem('username');
            }

            ElMessage.success('登录成功！');
            router.push('/debate-hall');
          }
        } catch (error) {
          console.error('登录失败:', error);

          if (error.response?.status === 401) {
            errors.general = '用户名或密码错误';
          } else if (error.response?.status === 400) {
            const errorData = error.response.data;
            if (errorData.field === 'captcha') {
              errors.captcha = errorData.message;
              await refreshCaptcha(); // 刷新验证码
            } else {
              errors.general = errorData.message || '登录失败，请重试';
            }
          } else {
            errors.general = error.response?.data?.message || '登录失败，请重试';
          }
        } finally {
          loading.value = false;
        }
      });
    }

    const togglePasswordVisibility = () => {
      showPassword.value = !showPassword.value
    }

    const goToRegister = () => {
      router.push('/register')
    }

    // 检查是否记住用户名
    const checkRememberMe = () => {
      const remembered = localStorage.getItem('rememberMe')
      if (remembered) {
        formData.username = localStorage.getItem('username') || ''
        formData.rememberMe = true
      }
    }

    // 组件挂载时初始化验证码
    onMounted(() => {
      initCaptcha();
      checkRememberMe();
    })

    return {
      formData,
      errors,
      loading,
      showPassword,
      captchaUrl,
      captchaLoading,
      handleLogin,
      togglePasswordVisibility,
      refreshCaptcha,
      goToRegister,
      rules,
      loginForm
    }
}

// 导出默认对象
export default {
  setup: useLogin
}
