package com.debate_ournament.users.service;

import java.time.LocalDateTime;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.users.entity.Captcha;
import com.debate_ournament.users.repository.CaptchaRepository;
import com.debate_ournament.util.CaptchaUtil;

/**
 * 验证码业务服务类
 */
@Service
@Transactional
public class CaptchaService {

    private static final Logger logger = LoggerFactory.getLogger(CaptchaService.class);

    private static final int CAPTCHA_EXPIRATION_MINUTES = 5;
    private static final int MAX_ATTEMPTS = 3;
    private static final int MAX_CAPTCHAS_PER_IP_PER_HOUR = 20;

    private final CaptchaRepository captchaRepository;
    private final CaptchaUtil captchaUtil;

    @Autowired
    public CaptchaService(CaptchaRepository captchaRepository, CaptchaUtil captchaUtil) {
        this.captchaRepository = captchaRepository;
        this.captchaUtil = captchaUtil;
    }

    /**
     * 生成验证码
     */
    public Captcha generateCaptcha(String sessionId, String clientIp) {
        logger.info("生成验证码: sessionId={}, clientIp={}", sessionId, clientIp);

        // 检查IP频率限制
        checkIpRateLimit(clientIp);

        // 删除旧的验证码
        captchaRepository.findBySessionId(sessionId).ifPresent(captchaRepository::delete);

        // 生成验证码
        CaptchaUtil.CaptchaResult result = captchaUtil.generateCaptcha();
        String code = result.getCode();

        // 创建验证码实体
        Captcha captcha = new Captcha(sessionId, code, CAPTCHA_EXPIRATION_MINUTES);
        captcha.setClientIp(clientIp);

        Captcha savedCaptcha = captchaRepository.save(captcha);
        logger.info("验证码生成成功: sessionId={}, code={}", sessionId, code);
        return savedCaptcha;
    }

    /**
     * 生成验证码图片Base64
     */
    @Transactional(readOnly = true)
    public String generateCaptchaImageBase64(String sessionId) {
        logger.debug("生成验证码图片: sessionId={}", sessionId);

        Optional<Captcha> captchaOpt = captchaRepository.findBySessionId(sessionId);
        if (captchaOpt.isEmpty()) {
            throw new IllegalArgumentException("验证码不存在");
        }

        Captcha captcha = captchaOpt.get();
        if (!captcha.isValid()) {
            throw new IllegalArgumentException("验证码已失效");
        }

        // 使用已保存的验证码生成图片
        CaptchaUtil.CaptchaResult result = captchaUtil.generateCaptchaWithCode(captcha.getCode());
        return result.getImageBase64();
    }

    /**
     * 验证验证码
     */
    public boolean verifyCaptcha(String sessionId, String inputCode) {
        logger.info("验证验证码: sessionId={}, inputCode={}", sessionId, inputCode);

        Optional<Captcha> captchaOpt = captchaRepository.findValidCaptcha(sessionId, LocalDateTime.now());
        if (captchaOpt.isEmpty()) {
            logger.warn("验证码不存在或已失效: sessionId={}", sessionId);
            return false;
        }

        Captcha captcha = captchaOpt.get();

        // 增加尝试次数
        captcha.incrementAttemptCount();
        captchaRepository.save(captcha);

        // 检查尝试次数
        if (captcha.exceedsMaxAttempts(MAX_ATTEMPTS)) {
            logger.warn("验证码尝试次数过多: sessionId={}, attempts={}", sessionId, captcha.getAttemptCount());
            captcha.markAsUsed();
            captchaRepository.save(captcha);
            return false;
        }

        // 验证验证码
        boolean isValid = captchaUtil.verifyCaptcha(inputCode, captcha.getCode());

        if (isValid) {
            logger.info("验证码验证成功: sessionId={}", sessionId);
            captcha.markAsUsed();
            captchaRepository.save(captcha);
        } else {
            logger.warn("验证码验证失败: sessionId={}, expected={}, actual={}",
                       sessionId, captcha.getCode(), inputCode);
        }

        return isValid;
    }

    /**
     * 刷新验证码
     */
    public Captcha refreshCaptcha(String sessionId, String clientIp) {
        logger.info("刷新验证码: sessionId={}, clientIp={}", sessionId, clientIp);

        // 检查IP频率限制
        checkIpRateLimit(clientIp);

        // 标记旧验证码为已使用
        captchaRepository.findBySessionId(sessionId).ifPresent(captcha -> {
            captcha.markAsUsed();
            captchaRepository.save(captcha);
        });

        // 生成新验证码
        return generateCaptcha(sessionId, clientIp);
    }

    /**
     * 检查验证码是否有效
     */
    @Transactional(readOnly = true)
    public boolean isCaptchaValid(String sessionId) {
        Optional<Captcha> captchaOpt = captchaRepository.findValidCaptcha(sessionId, LocalDateTime.now());
        return captchaOpt.isPresent();
    }

    /**
     * 获取验证码信息（不包含验证码内容）
     */
    @Transactional(readOnly = true)
    public Optional<Captcha> getCaptchaInfo(String sessionId) {
        return captchaRepository.findBySessionId(sessionId);
    }

    /**
     * 清理过期的验证码
     */
    @Transactional
    public int cleanupExpiredCaptchas() {
        logger.info("清理过期验证码");
        return captchaRepository.deleteExpiredCaptchas(LocalDateTime.now());
    }

    /**
     * 清理已使用的验证码
     */
    @Transactional
    public int cleanupUsedCaptchas() {
        logger.info("清理已使用验证码");
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(1);
        return captchaRepository.deleteUsedCaptchas(cutoffTime);
    }

    /**
     * 清理旧验证码
     */
    @Transactional
    public int cleanupOldCaptchas() {
        logger.info("清理旧验证码");
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(1);
        return captchaRepository.deleteCaptchasCreatedBefore(cutoffTime);
    }

    /**
     * 检查IP频率限制
     */
    private void checkIpRateLimit(String clientIp) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        long count = captchaRepository.countByClientIpAndCreatedAtAfter(clientIp, oneHourAgo);

        if (count >= MAX_CAPTCHAS_PER_IP_PER_HOUR) {
            logger.warn("IP请求验证码频率过高: ip={}, count={}", clientIp, count);
            throw new IllegalStateException("验证码请求过于频繁，请稍后再试");
        }
    }

    /**
     * 统计指定时间段内的验证码生成数量
     */
    @Transactional(readOnly = true)
    public long countCaptchasCreatedBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return captchaRepository.countCaptchasCreatedBetween(startTime, endTime);
    }

    /**
     * 获取指定IP的验证码历史
     */
    @Transactional(readOnly = true)
    public java.util.List<Captcha> getCaptchaHistoryByIp(String clientIp, LocalDateTime cutoffTime) {
        return captchaRepository.findCaptchaHistoryByIp(clientIp, cutoffTime);
    }
}
