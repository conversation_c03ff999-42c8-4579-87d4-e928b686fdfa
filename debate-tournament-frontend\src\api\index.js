import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { useEncryptionStore } from '@/store/encryption'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  async config => {
    const userStore = useUserStore()
    const encryptionStore = useEncryptionStore()

    // 添加认证token
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }

    // 检查是否需要加密传输
    if (shouldEncryptRequest(config)) {
      try {
        // 初始化加密系统
        await encryptionStore.initialize()

        // 加密请求数据
        if (config.data) {
          const encryptedRequest = encryptionStore.encryptRequest(config.data)
          config.data = encryptedRequest
        }

        // 标记为加密请求
        config.headers['X-Encrypted'] = 'true'
      } catch (error) {
        console.error('加密请求失败:', error)
        // 如果加密失败，可以选择降级到明文传输或抛出错误
        // 这里选择降级到明文传输
        console.warn('降级到明文传输')
      }
    }

    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  async response => {
    const encryptionStore = useEncryptionStore()

    // 检查是否为加密响应
    if (isEncryptedResponse(response)) {
      try {
        // 解密响应数据
        const decryptedData = encryptionStore.decryptResponse(response.data.data)
        response.data.data = decryptedData
      } catch (error) {
        console.error('解密响应失败:', error)
        ElMessage.error('数据解密失败，请重试')
        return Promise.reject(new Error('数据解密失败'))
      }
    }

    // 统一处理响应
    const res = response.data

    // 检查业务状态码
    if (res.code !== undefined) {
      if (res.code === 200 || res.success === true) {
        return res
      } else {
        // 业务错误
        const errorMessage = res.message || '请求失败'
        ElMessage.error(errorMessage)
        return Promise.reject(new Error(errorMessage))
      }
    }

    return res
  },
  error => {
    console.error('请求错误:', error)

    const userStore = useUserStore()

    // 处理HTTP状态码错误
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          ElMessage.error('未授权访问，请重新登录')
          userStore.logout()
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 423:
          ElMessage.error('账号已被锁定')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          const errorMessage = data?.message || `请求失败 (${status})`
          ElMessage.error(errorMessage)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请稍后重试')
    } else {
      ElMessage.error(error.message || '网络错误，请检查网络连接')
    }

    return Promise.reject(error)
  }
)

/**
 * 判断是否需要加密传输
 * @param {Object} config - axios配置
 * @returns {boolean}
 */
function shouldEncryptRequest(config) {
  // 临时禁用加密功能以解决网络问题
  if (config.forceNoEncrypt) {
    return false;
  }

  if (config.forceEncrypt) {
    return true;
  }

  // 敏感接口需要加密（暂时禁用注册接口加密）
  const sensitiveEndpoints = [
    '/api/auth/login',
    // '/api/auth/register', // 临时禁用注册加密
    '/api/auth/change-password'
  ]

  // 检查是否为敏感接口
  const isSensitive = sensitiveEndpoints.some(endpoint =>
    config.url?.includes(endpoint)
  )

  // 检查是否为POST/PUT请求（通常包含敏感数据）
  const isSensitiveMethod = ['post', 'put', 'patch'].includes(
    config.method?.toLowerCase()
  )

  // 检查是否有密码字段
  const hasPasswordField = config.data && (
    config.data.password ||
    config.data.oldPassword ||
    config.data.newPassword
  )

  // 暂时禁用所有加密以解决网络问题
  return false;
  // return isSensitive || (isSensitiveMethod && hasPasswordField)
}

/**
 * 判断是否为加密响应
 * @param {Object} response - axios响应
 * @returns {boolean}
 */
function isEncryptedResponse(response) {
  return response.data &&
         response.data.data &&
         typeof response.data.data === 'object' &&
         response.data.data.data &&
         response.data.data.signature
}

/**
 * 创建请求方法
 */
const request = {
  get(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      ...config
    })
  },

  post(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config
    })
  },

  put(url, data = {}, config = {}) {
    return service({
      method: 'put',
      url,
      data,
      ...config
    })
  },

  delete(url, config = {}) {
    return service({
      method: 'delete',
      url,
      ...config
    })
  },

  patch(url, data = {}, config = {}) {
    return service({
      method: 'patch',
      url,
      data,
      ...config
    })
  },

  // 强制加密请求
  encryptedPost(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config,
      forceEncrypt: true
    })
  },

  // 强制明文请求
  plaintextPost(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config,
      forceNoEncrypt: true
    })
  }
}

export default request
