<template>
  <div class="auth-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="auth-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo-section">
            <div class="logo">
              <el-icon size="48">
                <ChatRound />
              </el-icon>
            </div>
            <h1 class="brand-title">AI辩论赛</h1>
            <p class="brand-subtitle">探索AI辩论的无限可能</p>
          </div>

          <div class="features-list">
            <div class="feature-item">
              <el-icon>
                <ChatDotRound />
              </el-icon>
              <span>实时AI辩论对战</span>
            </div>
            <div class="feature-item">
              <el-icon>
                <Trophy />
              </el-icon>
              <span>智能排名系统</span>
            </div>
            <div class="feature-item">
              <el-icon>
                <Connection />
              </el-icon>
              <span>社区互动交流</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">欢迎回来</h2>
            <p class="form-subtitle">登录您的账户继续体验</p>
          </div>

          <el-form ref="loginForm" :model="formData" :rules="rules" class="auth-form" @submit.prevent="handleLogin"
            size="large">
            <!-- 用户名输入 -->
            <el-form-item prop="username">
              <el-input v-model="formData.username" placeholder="请输入用户名" prefix-icon="User" clearable />
            </el-form-item>

            <!-- 密码输入 -->
            <el-form-item prop="password">
              <el-input v-model="formData.password" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码"
                prefix-icon="Lock" clearable>
                <template #suffix>
                  <el-icon class="password-toggle" @click="togglePasswordVisibility">
                    <component :is="showPassword ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 验证码输入 -->
            <el-form-item prop="captcha">
              <div class="captcha-wrapper">
                <el-input v-model="formData.captcha" placeholder="请输入验证码" prefix-icon="Key" maxlength="4" clearable
                  class="captcha-input" />
                <div class="captcha-image-container" @click="refreshCaptcha">
                  <el-image v-if="captchaUrl" :src="captchaUrl" fit="fill" class="captcha-img"
                    :loading="captchaLoading">
                    <template #error>
                      <div class="captcha-error">
                        <el-icon>
                          <Warning />
                        </el-icon>
                        <span>点击刷新</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else class="captcha-loading">
                    <el-icon class="is-loading">
                      <Loading />
                    </el-icon>
                  </div>
                  <div class="captcha-refresh-hint">点击刷新</div>
                </div>
              </div>
            </el-form-item>

            <!-- 选项区域 -->
            <div class="form-options">
              <el-checkbox v-model="formData.rememberMe">记住我</el-checkbox>
              <router-link to="/forgot-password" class="forgot-link">
                忘记密码？
              </router-link>
            </div>

            <!-- 登录按钮 -->
            <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading" size="large" round>
              {{ loading ? '登录中...' : '登录' }}
            </el-button>

            <!-- 注册链接 -->
            <div class="register-prompt">
              还没有账号？
              <router-link to="/register" class="register-link">立即注册</router-link>
            </div>
          </el-form>

          <!-- 第三方登录 -->
          <div class="social-login">
            <div class="divider">
              <span>其他登录方式</span>
            </div>
            <div class="social-buttons">
              <el-tooltip content="微信登录" placement="top">
                <el-button circle class="social-btn wechat">
                  <el-icon>
                    <ChatDotRound />
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="QQ登录" placement="top">
                <el-button circle class="social-btn qq">
                  <el-icon>
                    <ChatLineSquare />
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="微博登录" placement="top">
                <el-button circle class="social-btn weibo">
                  <el-icon>
                    <Share />
                  </el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import LoginLogic from './js/Login.js';

// 使用登录逻辑
const loginLogic = LoginLogic.setup();

// 解构需要的变量和方法
const {
  formData,
  errors,
  loading,
  showPassword,
  captchaUrl,
  captchaLoading,
  handleLogin,
  togglePasswordVisibility,
  refreshCaptcha,
  goToRegister,
  rules,
  loginForm
} = loginLogic;
</script>

<style lang="scss" scoped>
@use './scss/Auth.scss';
</style>
