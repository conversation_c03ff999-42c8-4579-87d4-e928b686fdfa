<template>
  <div class="auth-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="auth-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo-section">
            <div class="logo">
              <el-icon size="48">
                <ChatRound />
              </el-icon>
            </div>
            <h1 class="brand-title">AI辩论赛</h1>
            <p class="brand-subtitle">探索AI辩论的无限可能</p>
          </div>

          <div class="features-list">
            <div class="feature-item">
              <el-icon>
                <ChatDotRound />
              </el-icon>
              <span>实时AI辩论对战</span>
            </div>
            <div class="feature-item">
              <el-icon>
                <Trophy />
              </el-icon>
              <span>智能排名系统</span>
            </div>
            <div class="feature-item">
              <el-icon>
                <Connection />
              </el-icon>
              <span>社区互动交流</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">欢迎回来</h2>
            <p class="form-subtitle">登录您的账户继续体验</p>
          </div>

          <el-form
            ref="loginForm"
            :model="formData"
            :rules="rules"
            class="auth-form"
            @submit.prevent="handleLogin"
            size="large"
          >
            <!-- 用户名输入 -->
            <el-form-item prop="username">
              <el-input
                v-model="formData.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
              />
            </el-form-item>

            <!-- 密码输入 -->
            <el-form-item prop="password">
              <el-input
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                prefix-icon="Lock"
                clearable
              >
                <template #suffix>
                  <el-icon class="password-toggle" @click="togglePasswordVisibility">
                    <component :is="showPassword ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 验证码输入 -->
            <el-form-item prop="captcha">
              <div class="captcha-wrapper">
                <el-input
                  v-model="formData.captcha"
                  placeholder="请输入验证码"
                  prefix-icon="Key"
                  maxlength="4"
                  clearable
                  class="captcha-input"
                />
                <div class="captcha-image-container" @click="refreshCaptcha">
                  <el-image
                    v-if="captchaUrl"
                    :src="captchaUrl"
                    fit="fill"
                    class="captcha-img"
                    :loading="captchaLoading"
                  >
                    <template #error>
                      <div class="captcha-error">
                        <el-icon><Warning /></el-icon>
                        <span>点击刷新</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else class="captcha-loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                  </div>
                  <div class="captcha-refresh-hint">点击刷新</div>
                </div>
              </div>
            </el-form-item>

            <!-- 选项区域 -->
            <div class="form-options">
              <el-checkbox v-model="formData.rememberMe">记住我</el-checkbox>
              <router-link to="/forgot-password" class="forgot-link">
                忘记密码？
              </router-link>
            </div>

            <!-- 登录按钮 -->
            <el-button
              type="primary"
              native-type="submit"
              class="submit-btn"
              :loading="loading"
              size="large"
              round
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>

            <!-- 注册链接 -->
            <div class="register-prompt">
              还没有账号？
              <router-link to="/register" class="register-link">立即注册</router-link>
            </div>
          </el-form>

          <!-- 第三方登录 -->
          <div class="social-login">
            <div class="divider">
              <span>其他登录方式</span>
            </div>
            <div class="social-buttons">
              <el-tooltip content="微信登录" placement="top">
                <el-button circle class="social-btn wechat">
                  <el-icon><ChatDotRound /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="QQ登录" placement="top">
                <el-button circle class="social-btn qq">
                  <el-icon><ChatLineSquare /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="微博登录" placement="top">
                <el-button circle class="social-btn weibo">
                  <el-icon><Share /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/user';
import LoginLogic from './js/Login.js';

// 使用登录逻辑
const loginLogic = LoginLogic.setup();

// 解构需要的变量和方法
const {
  formData,
  errors,
  loading,
  showPassword,
  captchaUrl,
  captchaLoading,
  handleLogin,
  togglePasswordVisibility,
  refreshCaptcha,
  goToRegister,
  rules,
  loginForm
} = loginLogic;
</script>

<style lang="scss" scoped>
// 全局变量
$primary-color: #409eff;
$primary-light: #79bbff;
$primary-dark: #337ecc;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

$border-color: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;

$background-color: #f5f7fa;
$background-color-light: #fafafa;

// 主容器
.auth-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

// 背景装饰
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

// 主容器
.auth-container {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
  min-height: 600px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    max-width: 400px;
    min-height: auto;
  }
}

// 品牌区域
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  @media (max-width: 768px) {
    padding: 30px 20px;
    min-height: 200px;
  }
}

.brand-content {
  text-align: center;
  z-index: 1;
  position: relative;
}

.logo-section {
  margin-bottom: 40px;
}

.logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.brand-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 0;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 20px;

  @media (max-width: 768px) {
    display: none;
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  opacity: 0.9;

  .el-icon {
    font-size: 20px;
  }
}

// 表单区域
.form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  @media (max-width: 768px) {
    padding: 30px 20px;
  }
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: 8px;
}

.form-subtitle {
  color: $text-secondary;
  font-size: 16px;
  margin: 0;
}

// 表单样式
.auth-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input {
    height: 48px;

    :deep(.el-input__inner) {
      height: 48px;
      border-radius: 12px;
      border: 2px solid $border-color-light;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
    }
  }
}

// 验证码样式
.captcha-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image-container {
  position: relative;
  width: 120px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid $border-color-light;
  transition: all 0.3s ease;

  &:hover {
    border-color: $primary-color;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba($primary-color, 0.2);
  }
}

.captcha-img {
  width: 100%;
  height: 100%;
}

.captcha-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: $background-color;
  color: $text-secondary;
  font-size: 12px;

  .el-icon {
    margin-bottom: 4px;
    font-size: 16px;
  }
}

.captcha-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: $background-color;
  color: $primary-color;
}

.captcha-refresh-hint {
  position: absolute;
  bottom: 2px;
  right: 4px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 0, 0, 0.5);
  padding: 2px 4px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.captcha-image-container:hover .captcha-refresh-hint {
  opacity: 1;
}

// 表单选项
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  font-size: 14px;
}

.forgot-link {
  color: $primary-color;
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: $primary-dark;
    text-decoration: underline;
  }
}

// 提交按钮
.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 24px;
  border-radius: 12px;
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  border: none;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary-color, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// 注册提示
.register-prompt {
  text-align: center;
  font-size: 14px;
  color: $text-regular;
  margin-bottom: 32px;
}

.register-link {
  color: $primary-color;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;

  &:hover {
    color: $primary-dark;
    text-decoration: underline;
  }
}

// 社交登录
.social-login {
  border-top: 1px solid $border-color-lighter;
  padding-top: 24px;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: $border-color-light;
  }

  span {
    padding: 0 16px;
    color: $text-secondary;
    font-size: 14px;
    background: white;
  }
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.social-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: 2px solid $border-color-light;
  background: white;
  color: $text-regular;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.wechat:hover {
    border-color: #07c160;
    color: #07c160;
  }

  &.qq:hover {
    border-color: #12b7f5;
    color: #12b7f5;
  }

  &.weibo:hover {
    border-color: #e6162d;
    color: #e6162d;
  }
}

// 密码切换按钮
.password-toggle {
  cursor: pointer;
  color: $text-secondary;
  transition: color 0.3s ease;

  &:hover {
    color: $primary-color;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .auth-page {
    padding: 10px;
  }

  .auth-container {
    border-radius: 16px;
  }

  .form-section {
    padding: 20px 16px;
  }

  .form-title {
    font-size: 24px;
  }

  .captcha-wrapper {
    flex-direction: column;
    gap: 8px;
  }

  .captcha-image-container {
    width: 100%;
    height: 40px;
  }
}
</style>
