<template>
  <div class="login-page">
    <div class="container">
      <div class="login-container">
        <div class="login-form-wrapper">
          <div class="login-header">
            <h1 class="login-title">登录</h1>
            <p class="login-subtitle">欢迎回到AI辩论赛平台</p>
          </div>

          <el-form ref="loginForm" :model="formData" :rules="rules" class="login-form" @submit.prevent="handleLogin">
            <el-form-item prop="username">
              <el-input v-model="formData.username" placeholder="用户名" prefix-icon="User" autocomplete="username" />
            </el-form-item>

            <el-form-item prop="password">
              <el-input v-model="formData.password" :type="showPassword ? 'text' : 'password'" placeholder="密码"
                prefix-icon="Lock" autocomplete="current-password">
                <template #suffix>
                  <el-icon class="password-toggle" @click="togglePasswordVisibility">
                    <component :is="showPassword ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="captcha" class="captcha-item">
              <div class="captcha-container">
                <el-input v-model="formData.captcha" placeholder="验证码" prefix-icon="Key" class="captcha-input" />
                <div class="captcha-image" @click="refreshCaptcha" v-loading="captchaLoading">
                  <el-image :src="captchaUrl" fit="contain" class="captcha-image">
                    <template #error>
                      <div class="captcha-error">
                        <el-icon>
                          <Warning />
                        </el-icon>
                        <span>点击刷新</span>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </el-form-item>

            <div class="form-options">
              <el-checkbox v-model="formData.rememberMe">记住我</el-checkbox>
              <router-link to="/forgot-password" class="forgot-password">
                忘记密码?
              </router-link>
            </div>

            <el-button type="primary" native-type="submit" class="login-button" :loading="loading" round>
              登录
            </el-button>

            <div class="register-link">
              还没有账号?
              <router-link to="/register">立即注册</router-link>
            </div>
          </el-form>

          <div class="social-login">
            <div class="divider">
              <span>其他登录方式</span>
            </div>
            <div class="social-icons">
              <el-tooltip content="微信登录" placement="top">
                <el-button circle class="social-icon">
                  <el-icon>
                    <ChatDotRound />
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="QQ登录" placement="top">
                <el-button circle class="social-icon">
                  <el-icon>
                    <ChatLineSquare />
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="微博登录" placement="top">
                <el-button circle class="social-icon">
                  <el-icon>
                    <Share />
                  </el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>

        <div class="login-banner">
          <div class="banner-content">
            <h2 class="banner-title">AI辩论赛</h2>
            <p class="banner-text">探索AI辩论的无限可能，加入我们的平台，体验智能辩论的魅力。</p>
            <div class="banner-features">
              <div class="feature">
                <el-icon>
                  <ChatRound />
                </el-icon>
                <span>实时AI辩论</span>
              </div>
              <div class="feature">
                <el-icon>
                  <Trophy />
                </el-icon>
                <span>竞赛排名</span>
              </div>
              <div class="feature">
                <el-icon>
                  <Connection />
                </el-icon>
                <span>社区互动</span>
              </div>
            </div>
          </div>
          <div class="banner-image-wrapper">
            <el-image src="/images/login-banner.jpg" fit="cover" class="banner-image">
              <template #error>
                <div class="banner-image-placeholder"></div>
              </template>
            </el-image>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/user';
import LoginLogic from './js/Login.js';

// 使用登录逻辑
const loginLogic = LoginLogic.setup();

// 解构需要的变量和方法
const {
  formData,
  errors,
  loading,
  showPassword,
  captchaUrl,
  captchaLoading,
  handleLogin,
  togglePasswordVisibility,
  refreshCaptcha,
  goToRegister,
  rules,
  loginForm
} = loginLogic;
</script>

<style lang="scss" scoped>
.login-page {
  min-height: calc(100vh - 64px);
  padding: var(--spacing-xl) 0;
  display: flex;
  align-items: center;
  background-color: var(--background-color-light);
}

.login-container {
  display: flex;
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
  min-height: 600px;

  @media (max-width: 992px) {
    flex-direction: column-reverse;
    max-width: 500px;
  }
}

.login-form-wrapper {
  flex: 1;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;

  @media (max-width: 992px) {
    padding: var(--spacing-lg);
  }
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.login-subtitle {
  color: var(--text-secondary);
  font-size: 16px;
}

.login-form {
  margin-bottom: var(--spacing-lg);
}

.captcha-item {
  display: flex;

  :deep(.el-form-item__content) {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.captcha-input {
  flex: 1;
}

.captcha-image-wrapper {
  position: relative;
  width: 120px;
  height: 40px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  cursor: pointer;
}

.captcha-image {
  width: 100%;
  height: 100%;
}

.captcha-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f7fa;
  color: var(--text-secondary);
  font-size: 12px;

  .el-icon {
    margin-bottom: 4px;
    font-size: 16px;
  }
}

.refresh-captcha {
  position: absolute;
  right: 5px;
  top: 5px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  padding: 4px;
  font-size: 14px;
  cursor: pointer;
  color: var(--primary-color);
  transition: all 0.3s;

  &:hover {
    transform: rotate(180deg);
    background-color: rgba(255, 255, 255, 0.9);
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.forgot-password {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;

  &:hover {
    text-decoration: underline;
  }
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  margin-bottom: var(--spacing-lg);
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: var(--text-regular);
  margin-bottom: var(--spacing-xl);

  a {
    color: var(--primary-color);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.social-login {
  margin-top: auto;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: var(--border-color-light);
  }

  span {
    padding: 0 var(--spacing-md);
    color: var(--text-secondary);
    font-size: 14px;
  }
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
}

.social-icon {
  font-size: 18px;
  color: var(--text-regular);
  transition: all 0.3s;

  &:hover {
    color: var(--primary-color);
    transform: translateY(-3px);
  }
}

.login-banner {
  flex: 1;
  background: linear-gradient(135deg, #409eff, #36d1dc);
  color: white;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  @media (max-width: 992px) {
    min-height: 200px;
  }
}

.banner-content {
  padding: var(--spacing-xl);
  z-index: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.banner-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.banner-text {
  font-size: 16px;
  margin-bottom: var(--spacing-xl);
  max-width: 400px;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.banner-features {
  margin-top: auto;
  display: flex;
  gap: var(--spacing-xl);

  @media (max-width: 992px) {
    display: none;
  }
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  .el-icon {
    font-size: 20px;
  }
}

.banner-image-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.2;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #36d1dc, #5b86e5);
}

.password-toggle {
  cursor: pointer;
  color: var(--text-secondary);

  &:hover {
    color: var(--primary-color);
  }
}

.captcha-container {
  display: flex;
  gap: 10px;
}

.captcha-image {
  height: 32px;
  cursor: pointer;
}

.captcha-image img {
  height: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}
</style>
