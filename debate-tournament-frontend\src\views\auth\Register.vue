<template>
  <div class="register-page">
    <div class="container">
      <div class="register-container">
        <div class="register-banner">
          <div class="banner-content">
            <h2 class="banner-title">加入AI辩论赛</h2>
            <p class="banner-text">创建账号，开启您的AI辩论之旅，体验智能辩论的魅力。</p>
            <div class="banner-features">
              <div class="feature">
                <el-icon>
                  <ChatRound />
                </el-icon>
                <span>实时AI辩论</span>
              </div>
              <div class="feature">
                <el-icon>
                  <Trophy />
                </el-icon>
                <span>竞赛排名</span>
              </div>
              <div class="feature">
                <el-icon>
                  <Connection />
                </el-icon>
                <span>社区互动</span>
              </div>
            </div>
          </div>
          <div class="banner-image-wrapper">
            <el-image src="/images/register-banner.jpg" fit="cover" class="banner-image">
              <template #error>
                <div class="banner-image-placeholder"></div>
              </template>
            </el-image>
          </div>
        </div>

        <div class="register-form-wrapper">
          <div class="register-header">
            <h1 class="register-title">注册</h1>
            <p class="register-subtitle">创建您的AI辩论赛账号</p>
          </div>

          <el-form ref="registerForm" :model="formData" :rules="rules" class="register-form"
            @submit.prevent="handleRegister">
            <el-form-item prop="username">
              <el-input v-model="formData.username" placeholder="用户名" prefix-icon="User" autocomplete="username" />
            </el-form-item>

            <el-form-item prop="email">
              <el-input v-model="formData.email" placeholder="邮箱" prefix-icon="Message" autocomplete="email" />
            </el-form-item>

            <el-form-item prop="password">
              <el-input v-model="formData.password" :type="showPassword ? 'text' : 'password'" placeholder="密码"
                prefix-icon="Lock" autocomplete="new-password">
                <template #suffix>
                  <el-icon class="password-toggle" @click="showPassword = !showPassword">
                    <component :is="showPassword ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <el-input v-model="formData.confirmPassword" :type="showConfirmPassword ? 'text' : 'password'"
                placeholder="确认密码" prefix-icon="Lock" autocomplete="new-password">
                <template #suffix>
                  <el-icon class="password-toggle" @click="showConfirmPassword = !showConfirmPassword">
                    <component :is="showConfirmPassword ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="captcha" class="captcha-item">
              <el-input v-model="formData.captcha" placeholder="验证码" prefix-icon="Key" class="captcha-input" />
              <div class="captcha-image-wrapper">
                <el-image :src="captchaUrl" fit="contain" class="captcha-image" @click="refreshCaptcha">
                  <template #error>
                    <div class="captcha-error">
                      <el-icon>
                        <Warning />
                      </el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
                <el-tooltip content="刷新验证码" placement="top">
                  <el-icon class="refresh-captcha" @click="refreshCaptcha">
                    <Refresh />
                  </el-icon>
                </el-tooltip>
              </div>
            </el-form-item>

            <el-form-item prop="agreeTerms">
              <el-checkbox v-model="formData.agreeTerms">
                我已阅读并同意
                <router-link to="/terms" class="agreement-link">服务条款</router-link>
                和
                <router-link to="/privacy" class="agreement-link">隐私政策</router-link>
              </el-checkbox>
            </el-form-item>

            <el-button type="primary" native-type="submit" class="register-button" :loading="loading" round>
              注册
            </el-button>

            <div class="login-link">
              已有账号?
              <router-link to="/login">立即登录</router-link>
            </div>
          </el-form>

          <div class="social-register">
            <div class="divider">
              <span>其他注册方式</span>
            </div>
            <div class="social-icons">
              <el-tooltip content="微信注册" placement="top">
                <el-button circle class="social-icon">
                  <el-icon>
                    <ChatDotRound />
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="QQ注册" placement="top">
                <el-button circle class="social-icon">
                  <el-icon>
                    <ChatLineSquare />
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="微博注册" placement="top">
                <el-button circle class="social-icon">
                  <el-icon>
                    <Share />
                  </el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/user';
import RegisterLogic from './js/Register.js';

// 使用注册逻辑
const registerLogic = RegisterLogic.setup();

// 解构需要的变量和方法
const {
  formData,
  errors,
  loading,
  showPassword,
  captchaUrl,
  captchaLoading,
  handleRegister,
  togglePasswordVisibility,
  refreshCaptcha,
  goToLogin,
  rules,
  registerForm
} = registerLogic;

// 添加确认密码显示状态
const showConfirmPassword = ref(false);
</script>

<style lang="scss" scoped>
.register-page {
  min-height: calc(100vh - 64px);
  padding: var(--spacing-xl) 0;
  display: flex;
  align-items: center;
  background-color: var(--background-color-light);
}

.register-container {
  display: flex;
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
  min-height: 600px;

  @media (max-width: 992px) {
    flex-direction: column;
    max-width: 500px;
  }
}

.register-form-wrapper {
  flex: 1;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;

  @media (max-width: 992px) {
    padding: var(--spacing-lg);
  }
}

.register-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.register-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.register-subtitle {
  color: var(--text-secondary);
  font-size: 16px;
}

.register-form {
  margin-bottom: var(--spacing-lg);
}

.captcha-item {
  display: flex;

  :deep(.el-form-item__content) {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.captcha-input {
  flex: 1;
}

.captcha-image-wrapper {
  position: relative;
  width: 120px;
  height: 40px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  cursor: pointer;
}

.captcha-image {
  width: 100%;
  height: 100%;
}

.captcha-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f7fa;
  color: var(--text-secondary);
  font-size: 12px;

  .el-icon {
    margin-bottom: 4px;
    font-size: 16px;
  }
}

.refresh-captcha {
  position: absolute;
  right: 5px;
  top: 5px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  padding: 4px;
  font-size: 14px;
  cursor: pointer;
  color: var(--primary-color);
  transition: all 0.3s;

  &:hover {
    transform: rotate(180deg);
    background-color: rgba(255, 255, 255, 0.9);
  }
}

.agreement-link {
  color: var(--primary-color);
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  margin-bottom: var(--spacing-lg);
}

.login-link {
  text-align: center;
  font-size: 14px;
  color: var(--text-regular);
  margin-bottom: var(--spacing-xl);

  a {
    color: var(--primary-color);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.social-register {
  margin-top: auto;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: var(--border-color-light);
  }

  span {
    padding: 0 var(--spacing-md);
    color: var(--text-secondary);
    font-size: 14px;
  }
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
}

.social-icon {
  font-size: 18px;
  color: var(--text-regular);
  transition: all 0.3s;

  &:hover {
    color: var(--primary-color);
    transform: translateY(-3px);
  }
}

.register-banner {
  flex: 1;
  background: linear-gradient(135deg, #409eff, #36d1dc);
  color: white;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  @media (max-width: 992px) {
    min-height: 200px;
  }
}

.banner-content {
  padding: var(--spacing-xl);
  z-index: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.banner-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.banner-text {
  font-size: 16px;
  margin-bottom: var(--spacing-xl);
  max-width: 400px;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.banner-features {
  margin-top: auto;
  display: flex;
  gap: var(--spacing-xl);

  @media (max-width: 992px) {
    display: none;
  }
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  .el-icon {
    font-size: 20px;
  }
}

.banner-image-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.2;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #36d1dc, #5b86e5);
}

.password-toggle {
  cursor: pointer;
  color: var(--text-secondary);

  &:hover {
    color: var(--primary-color);
  }
}
</style>
