2025-05-26 22:57:14.745 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 27812 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 22:57:14.746 [main] DEBUG c.d.AiDebateTournamentPlatformApplication - Running with Spring Boot v3.5.0, Spring v6.2.7
2025-05-26 22:57:14.747 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 22:57:17.944 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-26 22:57:18.037 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3197 ms
2025-05-26 22:57:19.632 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-26 22:57:21.332 [main] DEBUG org.hibernate.SQL - 
    alter table chat_messages 
       modify column ai_provider enum ('ALIBABA','ALIBABA_BAILIAN','ANTHROPIC','BAIDU','DEEPSEEK','MOONSHOT','OPENAI','OPENROUTER','SILICONFLOW','TENCENT','ZHIPU')
2025-05-26 22:57:21.347 [main] DEBUG org.hibernate.SQL - 
    alter table mindmap_responses 
       modify column mindmap_type enum ('ARGUMENT','COMPARISON','CONCEPT','FLOWCHART','HIERARCHY','KNOWLEDGE','NETWORK','TIMELINE')
2025-05-26 22:57:21.365 [main] DEBUG org.hibernate.SQL - 
    alter table mindmap_responses 
       modify column status enum ('CANCELLED','FAILED','PENDING','SUCCESS')
2025-05-26 22:57:21.386 [main] DEBUG org.hibernate.SQL - 
    alter table user_levels 
       modify column win_rate decimal(5,2) DEFAULT 0.00 COMMENT '胜率（百分比）' not null
2025-05-26 22:57:24.640 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-05-26 22:57:25.424 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 22:57:25.427 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复公钥
2025-05-26 22:57:25.428 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复私钥
2025-05-26 22:57:25.428 [main] DEBUG c.d.service.KeyManagementService - 成功加载现有密钥对
2025-05-26 22:57:25.428 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 22:57:25.989 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 109 mappings in 'requestMappingHandlerMapping'
2025-05-26 22:57:26.056 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /uploads/**, /avatars/**, /static/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-05-26 22:57:26.243 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 22:57:26.308 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name customUserDetailsService
2025-05-26 22:57:26.444 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter$$SpringCGLIB$$0, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-05-26 22:57:26.751 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-26 22:57:26.812 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-26 22:57:27.380 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-05-26 22:57:27.404 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 13.499 seconds (process running for 14.048)
2025-05-26 22:57:27.494 [main] DEBUG c.d.config.ApplicationStartupConfig - 主上传目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads
2025-05-26 22:57:27.495 [main] DEBUG c.d.config.ApplicationStartupConfig - 头像上传目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\avatars
2025-05-26 22:57:27.495 [main] DEBUG c.d.config.ApplicationStartupConfig - 临时文件目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\temp
2025-05-26 22:57:27.495 [main] DEBUG c.d.config.ApplicationStartupConfig - 备份目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\backup
2025-05-26 22:57:27.495 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 22:57:27.495 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 22:57:27.519 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 22:57:27.521 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 5, 活跃用户数: 5
2025-05-26 22:58:22.055 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-26 22:58:22.056 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-05-26 22:58:22.056 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-05-26 22:58:22.056 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-05-26 22:58:22.058 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@23344ec3
2025-05-26 22:58:22.059 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@5064fecc
2025-05-26 22:58:22.060 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-26 22:58:22.060 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-05-26 22:58:22.088 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /auth/captcha/generate
2025-05-26 22:58:22.105 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-05-26 22:58:22.113 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /auth/captcha/generate
2025-05-26 22:58:22.116 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/auth/captcha/generate", parameters={}
2025-05-26 22:58:22.118 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.debate_ournament.users.controller.CaptchaController#generateCaptcha(HttpServletRequest, HttpSession)
2025-05-26 22:58:22.163 [http-nio-8080-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, clientIp=127.0.0.1
2025-05-26 22:58:22.163 [http-nio-8080-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 22:58:22.218 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    delete c1_0 
    from
        captchas c1_0 
    where
        c1_0.expires_at<?
2025-05-26 22:58:22.270 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(c1_0.session_id) 
    from
        captchas c1_0 
    where
        c1_0.client_ip=? 
        and c1_0.created_at>?
2025-05-26 22:58:22.301 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used 
    from
        captchas c1_0 
    where
        c1_0.session_id=?
2025-05-26 22:58:22.504 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used 
    from
        captchas c1_0 
    where
        c1_0.session_id=?
2025-05-26 22:58:22.521 [http-nio-8080-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, code=TCRJ
2025-05-26 22:58:22.558 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        captchas
        (attempt_count, client_ip, code, created_at, expires_at, used, session_id) 
    values
        (?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:58:22.629 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-05-26 22:58:22.631 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ApiResponse{code=200, message='验证码生成成功', data={sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, message=验 (truncated)...]
2025-05-26 22:58:22.656 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-05-26 22:59:38.146 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/register
2025-05-26 22:59:38.148 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-05-26 22:59:38.149 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/register
2025-05-26 22:59:38.150 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/register", parameters={}
2025-05-26 22:59:38.150 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.debate_ournament.users.controller.AuthController#register(Object, HttpServletRequest)
2025-05-26 22:59:38.183 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{username=cleanloguser, email=<EMAIL>, password=password123, confirmPassword=passwo (truncated)...]
2025-05-26 22:59:38.238 [http-nio-8080-exec-5] INFO  c.d.users.service.AuthService - 用户注册请求: username=cleanloguser, email=<EMAIL>, ip=127.0.0.1
2025-05-26 22:59:38.239 [http-nio-8080-exec-5] INFO  c.d.users.service.CaptchaService - 验证验证码: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, inputCode=TCRJ
2025-05-26 22:59:38.243 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used 
    from
        captchas c1_0 
    where
        c1_0.session_id=? 
        and c1_0.used=0 
        and c1_0.expires_at>?
2025-05-26 22:59:38.249 [http-nio-8080-exec-5] INFO  c.d.users.service.CaptchaService - 验证码验证成功: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC
2025-05-26 22:59:38.250 [http-nio-8080-exec-5] INFO  c.d.users.service.UserService - 创建新用户: username=cleanloguser, email=<EMAIL>
2025-05-26 22:59:38.256 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.username=? 
    limit
        ?
2025-05-26 22:59:38.259 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    limit
        ?
2025-05-26 22:59:38.410 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    insert 
    into
        users
        (created_at, email, email_verified, last_login_attempt, last_login_ip, last_login_time, locked_until, login_attempts, password, status, updated_at, username) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:59:38.432 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    insert 
    into
        user_levels
        (created_at, current_streak, experience, experience_to_next_level, highest_streak, level, total_debates, total_wins, updated_at, user_id, win_rate) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:59:38.435 [http-nio-8080-exec-5] INFO  c.d.users.service.UserService - 用户等级记录创建成功: userId=8
2025-05-26 22:59:38.441 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    insert 
    into
        user_profiles
        (avatar_content_type, avatar_file_name, avatar_file_size, avatar_url, bio, birth_date, created_at, display_name, gender, is_profile_public, location, major_or_field, school_or_organization, show_email, show_real_name, updated_at, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:59:38.444 [http-nio-8080-exec-5] INFO  c.d.users.service.UserService - 用户资料记录创建成功: userId=8
2025-05-26 22:59:38.444 [http-nio-8080-exec-5] INFO  c.d.users.service.UserService - 用户创建成功: userId=8
2025-05-26 22:59:38.444 [http-nio-8080-exec-5] INFO  c.d.users.service.AuthService - 用户注册成功: userId=8
2025-05-26 22:59:38.454 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    update
        captchas 
    set
        attempt_count=?,
        client_ip=?,
        code=?,
        created_at=?,
        expires_at=?,
        used=? 
    where
        session_id=?
2025-05-26 22:59:38.468 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-05-26 22:59:38.468 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ApiResponse{code=200, message='注册成功', data={message=注册成功，请验证邮箱后登录, userId=8, email=cleanloguser@exam (truncated)...]
2025-05-26 22:59:38.471 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-05-26 23:00:21.680 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 24904 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:00:21.682 [main] DEBUG c.d.AiDebateTournamentPlatformApplication - Running with Spring Boot v3.5.0, Spring v6.2.7
2025-05-26 23:00:21.683 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:00:24.802 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-26 23:00:24.898 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3111 ms
2025-05-26 23:00:28.422 [main] DEBUG org.hibernate.SQL - 
    alter table chat_messages 
       modify column ai_provider enum ('ALIBABA','ALIBABA_BAILIAN','ANTHROPIC','BAIDU','DEEPSEEK','MOONSHOT','OPENAI','OPENROUTER','SILICONFLOW','TENCENT','ZHIPU')
2025-05-26 23:00:28.440 [main] DEBUG org.hibernate.SQL - 
    alter table mindmap_responses 
       modify column mindmap_type enum ('ARGUMENT','COMPARISON','CONCEPT','FLOWCHART','HIERARCHY','KNOWLEDGE','NETWORK','TIMELINE')
2025-05-26 23:00:28.454 [main] DEBUG org.hibernate.SQL - 
    alter table mindmap_responses 
       modify column status enum ('CANCELLED','FAILED','PENDING','SUCCESS')
2025-05-26 23:00:28.470 [main] DEBUG org.hibernate.SQL - 
    alter table user_levels 
       modify column win_rate decimal(5,2) DEFAULT 0.00 COMMENT '胜率（百分比）' not null
2025-05-26 23:00:32.405 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-05-26 23:00:33.215 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:00:33.218 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复公钥
2025-05-26 23:00:33.219 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复私钥
2025-05-26 23:00:33.220 [main] DEBUG c.d.service.KeyManagementService - 成功加载现有密钥对
2025-05-26 23:00:33.220 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:00:33.871 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 109 mappings in 'requestMappingHandlerMapping'
2025-05-26 23:00:33.935 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /uploads/**, /avatars/**, /static/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-05-26 23:00:34.132 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:00:34.200 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name customUserDetailsService
2025-05-26 23:00:34.350 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter$$SpringCGLIB$$0, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-05-26 23:00:34.670 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-26 23:00:34.737 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-26 23:00:35.273 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-26 23:00:35.319 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:00:35.337 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

