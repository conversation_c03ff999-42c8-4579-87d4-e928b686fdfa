// 全局变量
$primary-color: #409eff;
$primary-light: #79bbff;
$primary-dark: #337ecc;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

$border-color: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;

$background-color: #f5f7fa;
$background-color-light: #fafafa;

$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

/* 认证页面主容器 */
.auth-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 主容器 */
.auth-container {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
  min-height: 600px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    max-width: 400px;
    min-height: auto;
  }

  &.single-panel {
    max-width: 500px;
    justify-content: center;
  }
}

/* 品牌区域 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  @media (max-width: 768px) {
    padding: 30px 20px;
    min-height: 200px;
  }
}

.brand-content {
  text-align: center;
  z-index: 1;
  position: relative;
}

.logo-section {
  margin-bottom: 40px;
}

.logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.brand-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 0;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 20px;

  @media (max-width: 768px) {
    display: none;
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  opacity: 0.9;

  .el-icon {
    font-size: 20px;
  }
}

/* 表单区域 */
.form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  @media (max-width: 768px) {
    padding: 30px 20px;
  }
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: 8px;
}

.form-subtitle {
  color: $text-secondary;
  font-size: 16px;
  margin: 0;
}

/* 表单样式 */
.auth-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input {
    height: 48px;

    :deep(.el-input__inner) {
      height: 48px;
      border-radius: 12px;
      border: 2px solid $border-color-light;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
    }
  }
}

/* 验证码样式 */
.captcha-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image-container {
  position: relative;
  width: 120px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid $border-color-light;
  transition: all 0.3s ease;

  &:hover {
    border-color: $primary-color;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba($primary-color, 0.2);
  }
}

.captcha-img {
  width: 100%;
  height: 100%;
}

.captcha-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: $background-color;
  color: $text-secondary;
  font-size: 12px;

  .el-icon {
    margin-bottom: 4px;
    font-size: 16px;
  }
}

.captcha-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: $background-color;
  color: $primary-color;
}

.captcha-refresh-hint {
  position: absolute;
  bottom: 2px;
  right: 4px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 0, 0, 0.5);
  padding: 2px 4px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.captcha-image-container:hover .captcha-refresh-hint {
  opacity: 1;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  font-size: 14px;
}

.forgot-link {
  color: $primary-color;
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: $primary-dark;
    text-decoration: underline;
  }
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 24px;
  border-radius: 12px;
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  border: none;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary-color, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 注册提示 */
.register-prompt,
.login-prompt {
  text-align: center;
  font-size: 14px;
  color: $text-regular;
  margin-bottom: 32px;
}

.register-link,
.login-link {
  color: $primary-color;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;

  &:hover {
    color: $primary-dark;
    text-decoration: underline;
  }
}

/* 社交登录 */
.social-login {
  border-top: 1px solid $border-color-lighter;
  padding-top: 24px;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: $border-color-light;
  }

  span {
    padding: 0 16px;
    color: $text-secondary;
    font-size: 14px;
    background: white;
  }
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.social-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: 2px solid $border-color-light;
  background: white;
  color: $text-regular;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.wechat:hover {
    border-color: #07c160;
    color: #07c160;
  }

  &.qq:hover {
    border-color: #12b7f5;
    color: #12b7f5;
  }

  &.weibo:hover {
    border-color: #e6162d;
    color: #e6162d;
  }
}

/* 密码切换按钮 */
.password-toggle {
  cursor: pointer;
  color: $text-secondary;
  transition: color 0.3s ease;

  &:hover {
    color: $primary-color;
  }
}

/* 验证邮箱页面特定样式 */
.verification-status {
  text-align: center;
  padding: $spacing-lg 0;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid $text-secondary;
    border-top-color: $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto $spacing-md;
  }

  &.success {
    color: $success-color;
  }

  &.error {
    color: $danger-color;
  }

  .el-icon {
    font-size: 48px;
    margin-bottom: $spacing-md;
  }

  p {
    margin-bottom: $spacing-lg;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 注册页面特定样式 */
.agree-terms {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  margin-bottom: $spacing-lg;

  .el-checkbox {
    margin-right: $spacing-xs;
  }

  a {
    color: $primary-color;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

/* 状态页面样式 */
.status-container {
  text-align: center;
  padding: $spacing-xl;

  .status-icon {
    font-size: 64px;
    margin-bottom: $spacing-lg;

    &.success {
      color: $success-color;
    }

    &.error {
      color: $danger-color;
    }

    &.warning {
      color: $warning-color;
    }
  }

  .status-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: $spacing-md;
    color: $text-primary;
  }

  .status-message {
    font-size: 16px;
    color: $text-secondary;
    margin-bottom: $spacing-xl;
    line-height: 1.6;
  }

  .status-actions {
    display: flex;
    gap: $spacing-md;
    justify-content: center;
    flex-wrap: wrap;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-page {
    padding: 10px;
  }

  .auth-container {
    border-radius: 16px;
  }

  .form-section {
    padding: 20px 16px;
  }

  .form-title {
    font-size: 24px;
  }

  .captcha-wrapper {
    flex-direction: column;
    gap: 8px;
  }

  .captcha-image-container {
    width: 100%;
    height: 40px;
  }

  .status-container {
    padding: $spacing-lg;

    .status-icon {
      font-size: 48px;
    }

    .status-title {
      font-size: 20px;
    }

    .status-actions {
      flex-direction: column;
    }
  }
}

@media (max-width: 768px) {
  .brand-section {
    .features-list {
      display: none;
    }
  }

  .auth-container.single-panel {
    max-width: 90%;
  }
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid $border-color-light;
    border-top-color: $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

/* 错误提示 */
.error-message {
  background: rgba($danger-color, 0.1);
  border: 1px solid rgba($danger-color, 0.3);
  color: $danger-color;
  padding: $spacing-sm $spacing-md;
  border-radius: 8px;
  margin-bottom: $spacing-md;
  font-size: 14px;
}

.success-message {
  background: rgba($success-color, 0.1);
  border: 1px solid rgba($success-color, 0.3);
  color: $success-color;
  padding: $spacing-sm $spacing-md;
  border-radius: 8px;
  margin-bottom: $spacing-md;
  font-size: 14px;
}
