package com.debate_ournament.ai.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.ai.entity.MindMapNode;
import com.debate_ournament.ai.entity.MindMapResponse;
import com.debate_ournament.ai.entity.MindMapResponse.ResponseStatus;
import com.debate_ournament.ai.repository.MindMapResponseRepository;
import com.debate_ournament.ai.service.MindMapService;

/**
 * 思维导图服务实现类
 */
@Service
@Transactional
public class MindMapServiceImpl implements MindMapService {

    @Autowired
    private MindMapResponseRepository mindMapResponseRepository;

    @Override
    public MindMapResponse generateMindMap(String content, String sessionId, Long userId) {
        // 简单实现，创建一个基本的思维导图响应
        MindMapResponse response = new MindMapResponse();
        response.setUserId(userId);
        response.setSessionId(sessionId);
        response.setTitle("Generated Mind Map");
        response.setDescription("Auto-generated from content");
        response.setRequestText(content);
        response.setMindmapData("{}"); // 空的JSON数据
        response.setStatus(ResponseStatus.SUCCESS);
        response.setIsPublic(false);
        response.setCreatedAt(LocalDateTime.now());
        response.setUpdatedAt(LocalDateTime.now());

        return mindMapResponseRepository.save(response);
    }

    @Override
    public MindMapResponse generateMindMapFromConversation(String sessionId, Long userId) {
        // 简单实现
        return generateMindMap("Conversation content", sessionId, userId);
    }

    @Override
    public MindMapNode generateStructuredMindMap(String content, String topic) {
        // 简单实现，返回一个基本的节点结构
        MindMapNode root = new MindMapNode();
        root.setId("root");
        root.setText(topic);
        root.setLevel(0);
        root.setChildren(new ArrayList<>());
        return root;
    }

    @Override
    public String exportMindMap(Long mindMapId, String format) {
        // 简单实现
        return "Exported mind map in " + format + " format";
    }

    @Override
    public Page<MindMapResponse> getUserMindMaps(Long userId, Pageable pageable) {
        return mindMapResponseRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    @Override
    public void deleteMindMap(Long mindMapId, Long userId) {
        // 简单实现：直接删除，不检查用户权限
        mindMapResponseRepository.deleteById(mindMapId);
    }

    @Override
    public MindMapResponse updateMindMap(Long mindMapId, Long userId, String title, String description, Boolean isPublic, List<String> tags) {
        MindMapResponse mindMap = mindMapResponseRepository.findById(mindMapId)
                .orElseThrow(() -> new RuntimeException("Mind map not found"));

        if (title != null) mindMap.setTitle(title);
        if (description != null) mindMap.setDescription(description);
        if (isPublic != null) mindMap.setIsPublic(isPublic);
        mindMap.setUpdatedAt(LocalDateTime.now());

        return mindMapResponseRepository.save(mindMap);
    }

    @Override
    public MindMapResponse generateMindMapFromContent(Long userId, String content, String title, String description, Integer maxDepth, Integer maxNodes, Boolean isPublic, List<String> tags) {
        MindMapResponse response = new MindMapResponse();
        response.setUserId(userId);
        response.setTitle(title != null ? title : "Generated Mind Map");
        response.setDescription(description != null ? description : "Auto-generated from content");
        response.setRequestText(content);
        response.setMindmapData("{}"); // 空的JSON数据
        response.setStatus(ResponseStatus.SUCCESS);
        response.setIsPublic(isPublic != null ? isPublic : false);
        response.setCreatedAt(LocalDateTime.now());
        response.setUpdatedAt(LocalDateTime.now());

        return mindMapResponseRepository.save(response);
    }

    @Override
    public MindMapResponse generateMindMapFromSession(Long userId, String sessionId, String title, String description, Integer maxDepth, Integer maxNodes, Boolean isPublic, List<String> tags) {
        return generateMindMapFromContent(userId, "Session content", title, description, maxDepth, maxNodes, isPublic, tags);
    }

    @Override
    public CompletableFuture<MindMapResponse> generateMindMapAsync(Long userId, String content, String title, String description, Integer maxDepth, Integer maxNodes, Boolean isPublic, List<String> tags) {
        return CompletableFuture.supplyAsync(() ->
            generateMindMapFromContent(userId, content, title, description, maxDepth, maxNodes, isPublic, tags)
        );
    }

    @Override
    public Page<MindMapResponse> getPublicMindMaps(Pageable pageable, String sortBy, List<String> tags) {
        return mindMapResponseRepository.findPublicMindMaps(pageable);
    }

    @Override
    public Page<MindMapResponse> searchMindMaps(Long userId, String keyword, Pageable pageable, String sortBy, List<String> tags) {
        List<MindMapResponse> results = mindMapResponseRepository.findByUserIdAndTitleContaining(userId, keyword);
        return new PageImpl<>(results, pageable, results.size());
    }

    @Override
    public MindMapResponse getMindMapById(Long id, Long userId) {
        return mindMapResponseRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Mind map not found"));
    }

    @Override
    public String exportMindMapAsJson(Long id, Long userId) {
        MindMapResponse mindMap = getMindMapById(id, userId);
        return mindMap.getMindmapData();
    }

    @Override
    public String exportMindMapAsMarkdown(Long id, Long userId) {
        MindMapResponse mindMap = getMindMapById(id, userId);
        return "# " + mindMap.getTitle() + "\n\n" + mindMap.getDescription();
    }

    @Override
    public String exportMindMapAsXml(Long id, Long userId) {
        MindMapResponse mindMap = getMindMapById(id, userId);
        return "<mindmap><title>" + mindMap.getTitle() + "</title><description>" + mindMap.getDescription() + "</description></mindmap>";
    }

    @Override
    public String exportMindMapAsText(Long id, Long userId) {
        MindMapResponse mindMap = getMindMapById(id, userId);
        return mindMap.getTitle() + "\n" + mindMap.getDescription();
    }

    @Override
    public Map<String, Object> getUserMindMapStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        long totalCount = mindMapResponseRepository.countByUserId(userId);
        long publicCount = mindMapResponseRepository.countByUserIdAndStatus(userId, ResponseStatus.SUCCESS);

        stats.put("totalCount", totalCount);
        stats.put("publicCount", publicCount);
        stats.put("privateCount", totalCount - publicCount);

        return stats;
    }

    @Override
    public void batchDeleteMindMaps(List<Long> ids, Long userId) {
        // 简单实现：逐个删除
        for (Long id : ids) {
            mindMapResponseRepository.deleteById(id);
        }
    }

    @Override
    public MindMapResponse copyMindMap(Long id, Long userId, String newTitle) {
        MindMapResponse original = getMindMapById(id, userId);

        MindMapResponse copy = new MindMapResponse();
        copy.setUserId(userId);
        copy.setTitle(newTitle != null ? newTitle : "Copy of " + original.getTitle());
        copy.setDescription(original.getDescription());
        copy.setRequestText(original.getRequestText());
        copy.setMindmapData(original.getMindmapData());
        copy.setStatus(ResponseStatus.SUCCESS);
        copy.setIsPublic(false); // 复制的思维导图默认为私有
        copy.setCreatedAt(LocalDateTime.now());
        copy.setUpdatedAt(LocalDateTime.now());

        return mindMapResponseRepository.save(copy);
    }
}
