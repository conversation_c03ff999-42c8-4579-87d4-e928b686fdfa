<template>
  <div class="auth-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="auth-container single-panel">
      <!-- 表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">重置密码</h2>
            <p class="form-subtitle">请设置您的新密码</p>
          </div>

          <!-- 错误和成功消息 -->
          <div v-if="errors.general" class="error-message">
            {{ errors.general }}
          </div>

          <div v-if="success" class="success-message">
            {{ success }}
          </div>

          <el-form v-if="!success" ref="resetForm" :model="formData" :rules="rules" class="auth-form"
            @submit.prevent="handleSubmit" size="large">
            <!-- 新密码输入 -->
            <el-form-item prop="password">
              <el-input v-model="formData.password" :type="showPassword ? 'text' : 'password'" placeholder="请输入新密码"
                prefix-icon="Lock" clearable>
                <template #suffix>
                  <el-icon class="password-toggle" @click="showPassword = !showPassword">
                    <component :is="showPassword ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 确认密码输入 -->
            <el-form-item prop="confirmPassword">
              <el-input v-model="formData.confirmPassword" :type="showConfirmPassword ? 'text' : 'password'"
                placeholder="请再次输入新密码" prefix-icon="Lock" clearable>
                <template #suffix>
                  <el-icon class="password-toggle" @click="showConfirmPassword = !showConfirmPassword">
                    <component :is="showConfirmPassword ? 'View' : 'Hide'" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 提交按钮 -->
            <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading" size="large" round>
              {{ loading ? '重置中...' : '重置密码' }}
            </el-button>

            <!-- 返回登录链接 -->
            <div class="login-prompt">
              <router-link to="/login" class="login-link">返回登录</router-link>
            </div>
          </el-form>

          <!-- 成功状态 -->
          <div v-if="success" class="status-container">
            <div class="status-icon success">
              <el-icon>
                <CircleCheck />
              </el-icon>
            </div>
            <h3 class="status-title">密码重置成功</h3>
            <p class="status-message">
              您的密码已成功重置，现在可以使用新密码登录您的账户。
            </p>
            <div class="status-actions">
              <el-button type="primary" @click="goToLogin">立即登录</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ResetPasswordLogic from './js/ResetPassword.js';

// 使用重置密码逻辑
const resetPasswordLogic = ResetPasswordLogic.setup();

// 解构需要的变量和方法
const {
  formData,
  errors,
  loading,
  success,
  handleSubmit,
  goToLogin,
  rules,
  resetForm
} = resetPasswordLogic;

// 密码显示状态
const showPassword = ref(false);
const showConfirmPassword = ref(false);
</script>

<style lang="scss" scoped>
@use './scss/Auth.scss';
</style>
